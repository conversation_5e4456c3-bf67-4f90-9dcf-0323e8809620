@extends('owner.layouts.app')

@section('title', 'Gallery Management')

@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Gallery Management</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('owner.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Gallery</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <!-- Gallery Statistics -->
        <div class="row mb-3">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3 id="total-images">{{ $images->total() }}</h3>
                        <p>Total Images</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-images"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3 id="active-images">{{ $business->activeGalleryImages()->count() }}</h3>
                        <p>Active Images</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-eye"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3 id="featured-images">{{ $business->featuredGalleryImages()->count() }}</h3>
                        <p>Featured Images</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3 id="total-categories">{{ $categories->count() }}</h3>
                        <p>Categories</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-tags"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Management Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Gallery Images</h3>
                <div class="card-tools">
                    <a href="{{ route('owner.gallery.categories.index') }}" class="btn btn-secondary btn-sm mr-2">
                        <i class="fas fa-tags mr-1"></i>
                        Manage Categories
                    </a>
                    <a href="{{ route('owner.gallery.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus mr-1"></i>
                        Upload Images
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Filters and Search -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <form method="GET" action="{{ route('owner.gallery.index') }}" id="filter-form">
                            <select name="category" class="form-control" onchange="document.getElementById('filter-form').submit();">
                                <option value="">All Categories</option>
                                @foreach($categories as $cat)
                                    <option value="{{ $cat->id }}" {{ $categoryId == $cat->id ? 'selected' : '' }}>
                                        {{ $cat->name }} ({{ $cat->image_count }})
                                    </option>
                                @endforeach
                            </select>
                            <input type="hidden" name="search" value="{{ $search }}">
                            <input type="hidden" name="sort" value="{{ $sortBy }}">
                            <input type="hidden" name="order" value="{{ $sortOrder }}">
                        </form>
                    </div>
                    <div class="col-md-6">
                        <form method="GET" action="{{ route('owner.gallery.index') }}">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="Search images..." value="{{ $search }}">
                                <input type="hidden" name="category" value="{{ $categoryId }}">
                                <input type="hidden" name="sort" value="{{ $sortBy }}">
                                <input type="hidden" name="order" value="{{ $sortOrder }}">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group float-right" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="bulk-select-btn">
                                <i class="fas fa-check-square"></i> Bulk Select
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" id="bulk-delete-btn" style="display: none;">
                                <i class="fas fa-trash"></i> Delete Selected
                            </button>
                        </div>
                    </div>
                </div>

                @if($images->count() > 0)
                    <!-- Image Grid -->
                    <div class="row" id="gallery-grid">
                        @foreach($images as $image)
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4" data-image-id="{{ $image->id }}">
                                <div class="card gallery-image-card">
                                    <div class="image-container position-relative">
                                        <img src="{{ $image->thumbnail_url }}" alt="{{ $image->alt_text }}" class="card-img-top gallery-thumbnail">

                                        <!-- Image Overlay -->
                                        <div class="image-overlay">
                                            <div class="overlay-actions">
                                                <button class="btn btn-sm btn-light" onclick="viewImage({{ $image->id }})" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <a href="{{ route('owner.gallery.edit', $image) }}" class="btn btn-sm btn-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-warning toggle-featured" data-id="{{ $image->id }}" title="Toggle Featured">
                                                    <i class="fas fa-star {{ $image->is_featured ? 'text-warning' : '' }}"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="deleteImage({{ $image->id }})" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Bulk Select Checkbox -->
                                        <div class="bulk-select-checkbox" style="display: none;">
                                            <input type="checkbox" class="image-checkbox" value="{{ $image->id }}">
                                        </div>

                                        <!-- Status Badges -->
                                        <div class="status-badges">
                                            @if($image->is_featured)
                                                <span class="badge badge-warning">Featured</span>
                                            @endif
                                            @if(!$image->is_active)
                                                <span class="badge badge-secondary">Inactive</span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="card-body p-2">
                                        <h6 class="card-title mb-1">{{ $image->title ?: 'Untitled' }}</h6>
                                        <small class="text-muted">
                                            @if($image->category)
                                                <span class="badge badge-info">{{ $image->category->name }}</span>
                                            @endif
                                            <br>
                                            {{ $image->formatted_file_size }} • {{ $image->width }}x{{ $image->height }}
                                            <br>
                                            {{ $image->uploaded_at->format('M j, Y') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $images->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-4x text-muted mb-3"></i>
                        <h4>No Images Found</h4>
                        <p class="text-muted">
                            @if($search || $categoryId)
                                No images match your current filters. <a href="{{ route('owner.gallery.index') }}">Clear filters</a>
                            @else
                                Start building your gallery by uploading your first images.
                            @endif
                        </p>
                        @if(!$search && !$categoryId)
                            <a href="{{ route('owner.gallery.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i>
                                Upload Your First Images
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Image View Modal -->
<div class="modal fade" id="imageViewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageViewModalTitle">Image Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="imageViewModalImage" src="" alt="" class="img-fluid mb-3">
                <div id="imageViewModalDetails"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.gallery-image-card {
    transition: transform 0.2s;
}

.gallery-image-card:hover {
    transform: translateY(-2px);
}

.image-container {
    position: relative;
    overflow: hidden;
}

.gallery-thumbnail {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.image-container:hover .image-overlay {
    opacity: 1;
}

.overlay-actions {
    display: flex;
    gap: 5px;
}

.status-badges {
    position: absolute;
    top: 10px;
    left: 10px;
}

.bulk-select-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
}

.bulk-select-checkbox input[type="checkbox"] {
    transform: scale(1.2);
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Bulk select functionality
    let bulkSelectMode = false;

    $('#bulk-select-btn').click(function() {
        bulkSelectMode = !bulkSelectMode;

        if (bulkSelectMode) {
            $('.bulk-select-checkbox').show();
            $(this).text('Cancel Selection').removeClass('btn-outline-secondary').addClass('btn-secondary');
            $('#bulk-delete-btn').show();
        } else {
            $('.bulk-select-checkbox').hide();
            $('.image-checkbox').prop('checked', false);
            $(this).text('Bulk Select').removeClass('btn-secondary').addClass('btn-outline-secondary');
            $('#bulk-delete-btn').hide();
        }
    });

    // Bulk delete
    $('#bulk-delete-btn').click(function() {
        const selectedImages = $('.image-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedImages.length === 0) {
            toastr.warning('Please select images to delete.');
            return;
        }

        if (confirm(`Are you sure you want to delete ${selectedImages.length} selected image(s)?`)) {
            $.post('{{ route("owner.gallery.bulk-delete") }}', {
                image_ids: selectedImages,
                _token: '{{ csrf_token() }}'
            }).done(function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error(response.message);
                }
            }).fail(function() {
                toastr.error('Failed to delete images.');
            });
        }
    });

    // Toggle featured status
    $('.toggle-featured').click(function() {
        const imageId = $(this).data('id');
        const button = $(this);

        $.post(`/owner/gallery/${imageId}/toggle-featured`, {
            _token: '{{ csrf_token() }}'
        }).done(function(response) {
            if (response.success) {
                button.find('i').toggleClass('text-warning');
                toastr.success(response.message);
            } else {
                toastr.error(response.message);
            }
        }).fail(function() {
            toastr.error('Failed to update image status.');
        });
    });
});

// View image function
function viewImage(imageId) {
    // This would load image details via AJAX
    $('#imageViewModal').modal('show');
}

// Delete image function
function deleteImage(imageId) {
    if (confirm('Are you sure you want to delete this image?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/owner/gallery/${imageId}`;

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = '{{ csrf_token() }}';

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
